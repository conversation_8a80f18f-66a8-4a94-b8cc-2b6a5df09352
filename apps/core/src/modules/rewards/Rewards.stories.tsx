import { RewardsWidget } from '@modules/rewards'
import type { DynamicallyRenderedRewardsConfigType } from '@modules/rewards/Rewards.schema'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof RewardsWidget> = {
  title: 'Modules/Rewards/RewardsWidget',
  component: RewardsWidget,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'The Rewards module displays a horizontal scrolling list of reward cards with mock data. ' +
          'It is used as part of the dynamic content system and renders multiple RewardCard components ' +
          'with simple horizontal scrolling functionality. The mock data will be replaced with API calls once the RL is ready.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      title: 'My Rewards',
    } as DynamicallyRenderedRewardsConfigType,
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration object containing only the title (items are mocked internally)',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '100%',
          maxWidth: '1200px',
          padding: '20px',
          background: '#f8f9fa',
          borderRadius: '8px',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const WithTitle: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Rewards module with a custom title. The rewards data is mocked internally.',
      },
    },
  },
  args: {
    config: {
      title: 'Special Rewards',
    },
  },
}

export const WithoutTitle: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Rewards module without a title. The rewards data is mocked internally.',
      },
    },
  },
  args: {
    config: {},
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive Rewards module. You can customize the title, but the rewards data is mocked internally until the API is ready.',
      },
    },
  },
  render: args => (
    <div
      style={{
        width: '100%',
        maxWidth: '1200px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
      }}>
      <RewardsWidget {...args} />
    </div>
  ),
}
