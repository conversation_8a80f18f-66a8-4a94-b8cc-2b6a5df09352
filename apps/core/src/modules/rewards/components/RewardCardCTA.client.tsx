'use client'

import { But<PERSON> } from '@components/Button'
import { DynamicLink } from '@components/DynamicLink'
import { useIsMobile } from '@repo/hooks/useMobile'
import styles from '@modules/rewards/components/RewardCard.module.scss'

export interface IRewardCardCTAProps {
  ctaLabel: string
  ctaHref?: string
  ctaActionKey?: string
}

export const RewardCardCTA = ({ ctaLabel, ctaHref, ctaActionKey: _ctaActionKey }: IRewardCardCTAProps) => {
  const isMobile = useIsMobile()

  return (
    <div className={styles.ctaButton}>
      <Button
        as={ctaHref ? DynamicLink : 'button'}
        {...(ctaHref ? { href: ctaHref } : {})}
        label={ctaLabel}
        color="primary"
        size={isMobile ? 'sm' : 'md'}
        fullWidth={!isMobile}
        isTruncated
      />
    </div>
  )
}
