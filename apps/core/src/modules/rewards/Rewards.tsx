import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { RewardCard } from '@modules/rewards/components'
import type { DynamicallyRenderedRewardsConfigType } from '@modules/rewards/Rewards.schema'
import styles from '@modules/rewards/Rewards.module.scss'

export interface IRewardsWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardsConfigType
}

// TODO: Replace with RL request once ready
const rewards = [
  {
    title: '100 FS at Hercules Son of Zeus',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  {
    title: '50 FS at Book of Dead',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  {
    title: '25 FS at Starburst',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  {
    title: "200 FS at Gonzo's Quest",
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  {
    title: '75 FS at Mega Moolah',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  {
    title: '150 FS at Immortal Romance',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
]

const RewardsWidget: FC<IRewardsWidgetProps> = ({ config }) => {
  if (!config) {
    return null
  }

  const { title = 'My Rewards' } = config

  return (
    <div className={styles.container}>
      {!!title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        {rewards.map((reward, index) => (
          <div key={`${reward.title}-${index}`}>
            <RewardCard {...reward} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default RewardsWidget
