import 'server-only'
import { envVars } from '@/env'
import { BaseServerFetchApi } from '@/network/server-utils/BaseServerFetchApi'
import { getRLRequestParamsByLocale } from '@/utils/locale'
import type { Locale } from '@constants/locale'
import { s3Endpoints } from '@repo/api/endpoints/s3'
import type { GetCasinoConfigResponse } from '@repo/types/api/s3/casino-config'
import type { GetCasinoPageLayoutConfigGlobalResponse } from '@repo/types/api/s3/casino-page-config'
import type { GetAppConfigGlobalResponse, GetAppConfigResponse } from '@repo/types/api/s3/configs'
import type { IFeaturedOfferConfig } from '@repo/types/api/s3/featured-offer-config'
import type { GetHomePageConfigResponse } from '@repo/types/api/s3/home-page-config'
import type { GetPromotionsPageConfigResponse } from '@repo/types/api/s3/promotions-page-config'
import type { GetSidebarConfigGlobalResponse } from '@repo/types/api/s3/sidebar-config'
import type { GetSidemenuConfigResponse } from '@repo/types/api/s3/sidemenu-config'
import type { GetTimerComponentConfigResponse } from '@repo/types/api/s3/timer-component'
import type { WelcomePageV2Response } from '@repo/types/api/s3/welcome-page-v2'
import type { IRlGame } from '@repo/types/games'

class S3FetchApi extends BaseServerFetchApi {
  baseUrl = envVars.NEXT_PUBLIC_S3_URL
  revalidate = 15
  defaultHeaders = {
    Accept: 'application/json',
  }

  async getAllGamesList() {
    // response over 2MB cannot be cached
    return this.fetch<IRlGame[]>(s3Endpoints.gamesList, {
      next: { revalidate: 0 }, // Disable caching for large response
    })
  }

  async getAppConfigGlobal() {
    return this.fetch<GetAppConfigGlobalResponse>(s3Endpoints.appConfigGlobal)
  }

  async getAppConfig(locale: Locale) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<GetAppConfigResponse>(s3Endpoints.appConfig(rlParams.iso2, rlParams.market))
  }

  async getWelcomePageConfig(locale: Locale) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<WelcomePageV2Response>(s3Endpoints.welcomePage(rlParams.iso2, rlParams.market))
  }

  async getSidebarConfig(options?: RequestInit) {
    return this.fetch<GetSidebarConfigGlobalResponse>(s3Endpoints.sidebarConfigGlobal, options)
  }

  async getCasinoPageLayoutConfigGlobal(options?: RequestInit) {
    return this.fetch<GetCasinoPageLayoutConfigGlobalResponse>(s3Endpoints.casinoPageConfigGlobal, options)
  }

  async getLiveCasinoPageLayoutConfigGlobal(options?: RequestInit) {
    return this.fetch<GetCasinoPageLayoutConfigGlobalResponse>(s3Endpoints.liveCasinoPageConfigGlobal, options)
  }

  async getCasinoConfig(locale: Locale) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<GetCasinoConfigResponse>(s3Endpoints.casinoConfig(rlParams.iso2, rlParams.market))
  }

  async getLiveCasinoConfig(locale: Locale) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<GetCasinoConfigResponse>(s3Endpoints.liveCasinoConfig(rlParams.iso2, rlParams.market))
  }

  async getTimerComponentConfig(options?: RequestInit) {
    return this.fetch<GetTimerComponentConfigResponse>(s3Endpoints.timerComponentConfigGlobal, options)
  }

  async getFeaturedOfferConfig(locale: Locale) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<IFeaturedOfferConfig>(s3Endpoints.featuredOffer(rlParams.iso2, rlParams.market))
  }

  async getSidemenuConfig(options?: RequestInit) {
    return this.fetch<GetSidemenuConfigResponse>(s3Endpoints.sidemenuConfigGlobal, options)
  }

  async getHomePageConfig(options?: RequestInit) {
    return this.fetch<GetHomePageConfigResponse>(s3Endpoints.homePageConfigGlobal, options)
  }

  async getPromotionsPageConfig(options?: RequestInit) {
    return this.fetch<GetPromotionsPageConfigResponse>(s3Endpoints.promotionsPageConfigGlobal, options)
  }
}

export const s3FetchApi = new S3FetchApi()
